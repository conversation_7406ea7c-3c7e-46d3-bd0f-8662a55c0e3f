<Window x:Class="TaskReminderApp.Views.TaskEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TaskReminderApp.ViewModels"
        mc:Ignorable="d"
        Title="Add/Edit Task" Height="400" Width="500"
        WindowStartupLocation="CenterOwner">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="40"/>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="Task Title:" Margin="0,0,0,5"/>
            <TextBox Text="{Binding TaskTitle}" Margin="0,0,0,10"/>
            <TextBlock Text="Description:" Margin="0,0,0,5"/>
            <TextBox Text="{Binding TaskDescription}" Height="60" TextWrapping="Wrap" Margin="0,0,0,10"/>
            <Grid Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0">
                    <TextBlock Text="Due Date:" Margin="0,0,0,5"/>
                    <DatePicker SelectedDate="{Binding TaskDueDate}"/>
                </StackPanel>
                <StackPanel Grid.Column="1">
                    <TextBlock Text="Due Time:" Margin="0,0,0,5"/>
                    <TextBox Text="{Binding TaskDueTime}" Margin="5,0,0,0"/>
                </StackPanel>
            </Grid>
            <TextBlock Text="Priority:" Margin="0,0,0,5"/>
            <ComboBox SelectedIndex="{Binding PriorityIndex}" Margin="0,0,0,10">
                <ComboBoxItem Content="None"/>
                <ComboBoxItem Content="Low"/>
                <ComboBoxItem Content="Medium"/>
                <ComboBoxItem Content="High"/>
            </ComboBox>
        </StackPanel>
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Content="OK" Width="80" Margin="5" Click="OK_Click"/>
            <Button Content="Cancel" Width="80" Margin="5" Click="Cancel_Click"/>
        </StackPanel>
    </Grid>
</Window>