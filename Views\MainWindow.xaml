﻿<Window x:Class="TaskReminderApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TaskReminderApp.ViewModels"
        mc:Ignorable="d"
        Title="{Binding AppTitle}" Height="600" Width="1000">
    <Window.Resources>
        <Style TargetType="Button" x:Key="NavButtonStyle">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
        </Style>
        <Style TargetType="ListBoxItem">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
    </Window.Resources>
    <Window.CommandBindings>
        <CommandBinding Command="help" Executed="{Binding Help_Command}"/>
        <CommandBinding Command="Close" Executed="{Binding Exit_Command}"/>
    </Window.CommandBindings>
    <DockPanel>
        <!-- Menu -->
        <Menu DockPanel.Dock="Top">
            <MenuItem Header="File">
                <MenuItem Header="Exit" Command="{Binding Exit_Command}"/>
            </MenuItem>
            <MenuItem Header="Help">
                <MenuItem Header="Help" Command="{Binding Help_Command}"/>
            </MenuItem>
        </Menu>
        <!-- Header -->
        <Grid DockPanel.Dock="Top" Height="50" Background="#FFE0E0E0">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="Task Reminder App" FontSize="18" FontWeight="Bold" Margin="10,0,0,0"/>
                <TextBlock Text="{Binding CurrentDateTime}" FontSize="14" Margin="20,0,0,0"/>
            </StackPanel>
        </Grid>
        <!-- Footer -->
        <Grid DockPanel.Dock="Bottom" Height="30" Background="#FFE0E0E0">
            <TextBlock Text="{Binding StatusBarText}" VerticalAlignment="Center" HorizontalAlignment="Left" Margin="10,0,0,0"/>
        </Grid>
        <!-- Main Content -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <!-- Sidebar -->
            <StackPanel Grid.Column="0" Background="#FFF0F0F0" Margin="5">
                <TextBlock Text="Navigation" FontWeight="Bold" Margin="5"/>
                <Button Content="All Tasks" Style="{StaticResource NavButtonStyle}"/>
                <Button Content="Today" Style="{StaticResource NavButtonStyle}"/>
                <Button Content="Upcoming" Style="{StaticResource NavButtonStyle}"/>
                <Button Content="Completed" Style="{StaticResource NavButtonStyle}"/>
                <Button Content="Categories" Style="{StaticResource NavButtonStyle}"/>
                <TextBlock Text="Actions" FontWeight="Bold" Margin="5"/>
                <Button Content="Add Task" Style="{StaticResource NavButtonStyle}"/>
                <Button Content="Settings" Style="{StaticResource NavButtonStyle}"/>
            </StackPanel>
            <!-- Main Panel -->
            <Grid Grid.Column="1" Margin="5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <!-- Search and Filters -->
                <StackPanel Orientation="Horizontal" Grid.Row="0" Margin="0,0,0,5">
                    <TextBox Width="200" Margin="5" Text="Search tasks..." />
                    <Button Content="Search" Margin="5"/>
                    <Button Content="Clear" Margin="5"/>
                </StackPanel>
                <!-- Task List -->
                <ListBox Grid.Row="1" ItemsSource="{Binding Tasks}" DisplayMemberPath="Title" />
            </Grid>
        </Grid>
    </DockPanel>
</Window>
