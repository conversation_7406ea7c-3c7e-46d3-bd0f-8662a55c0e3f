﻿<Window x:Class="TaskReminderApp.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:TaskReminderApp.ViewModels"
        mc:Ignorable="d"
        Title="Task Reminder App" Height="600" Width="1000">
    <Window.Resources>
        <Style TargetType="Button" x:Key="NavButtonStyle">
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
        </Style>
        <Style TargetType="ListBoxItem">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
    </Window.Resources>
    <Grid>
        <TextBlock Text="Task Reminder App - Test Version"
                   HorizontalAlignment="Center"
                   VerticalAlignment="Center"
                   FontSize="24"
                   FontWeight="Bold"/>
    </Grid>
</Window>
