using System.ComponentModel.DataAnnotations;

namespace TaskReminderApp.Models
{
    public class AppSettings
    {
        [Key]
        public int Id { get; set; } = 1; // Singleton

        public int DefaultReminderMinutes { get; set; } = 15;

        public string Theme { get; set; } = "Light";

        public bool StartWithWindows { get; set; } = false;

        public bool EnableSound { get; set; } = true;

        public string DataPath { get; set; } = "";

        public bool AutoBackup { get; set; } = true;

        public DateTime LastBackupDate { get; set; }
    }
}