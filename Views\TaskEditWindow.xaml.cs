using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using TaskReminderApp.Services;
using TaskReminderApp.Models;

using TaskModel = TaskReminderApp.Models.Task;
namespace TaskReminderApp.Views
{
    /// <summary>
    /// Interaction logic for TaskEditWindow.xaml
    /// </summary>
    public partial class TaskEditWindow : Window
    {
        public TaskEditViewModel ViewModel;

        public TaskEditWindow()
        {
            InitializeComponent();
            ViewModel = new TaskEditViewModel();
            DataContext = ViewModel;
        }

        private void OK_Click(object sender, RoutedEventArgs e)
        {
            // Save the task
            if (ViewModel.SaveTask())
            {
                DialogResult = true;
                Close();
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }

    public class TaskEditViewModel
    {
        private TaskModel _task;
        private DataService _dataService;

        public TaskEditViewModel()
        {
            _task = new TaskModel();
            _dataService = new DataService();
            TaskTitle = "";
            TaskDescription = "";
            TaskDueDate = DateTime.Now;
            TaskDueTime = DateTime.Now.ToShortTimeString();
            PriorityIndex = 0;
        }

        public string TaskTitle
        {
            get => _task.Title;
            set => _task.Title = value;
        }

        public string TaskDescription
        {
            get => _task.Description;
            set => _task.Description = value;
        }

        public DateTime TaskDueDate
        {
            get => _task.DueDate ?? DateTime.Now;
            set => _task.DueDate = value;
        }

        public string TaskDueTime
        {
            get => ""; // simplify
            set { } // parse and combine
        }

        public int PriorityIndex
        {
            get => _task.Priority;
            set => _task.Priority = value;
        }

        public bool SaveTask()
        {
            try
            {
                _dataService.InsertTask(_task);
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving task: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }
    }
}