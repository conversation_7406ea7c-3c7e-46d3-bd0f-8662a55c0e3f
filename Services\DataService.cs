using TaskModel = TaskReminderApp.Models.Task;
using Microsoft.Data.Sqlite;
using System.Configuration;
using Serilog;
using TaskReminderApp.Models;
using System;
using System.Collections.Generic;
using System.IO;

namespace TaskReminderApp.Services
{
    public class DataService : IDisposable
    {
        private readonly string _connectionString;
        private SqliteConnection _connection;

        public DataService()
        {
            var config = ConfigurationManager.ConnectionStrings["TaskReminderDB"]?.ConnectionString;
            if (string.IsNullOrEmpty(config))
            {
                config = @"Data Source=%AppData%\TaskReminderApp\taskreminder.db;Version=3;";
            }
            _connectionString = Environment.ExpandEnvironmentVariables(config);
            EnsureDatabaseExists();
            InitializeSchema();
        }

        private void EnsureDatabaseExists()
        {
            var path = _connectionString.Split('=')[1].Split(';')[0];
            var directory = Path.GetDirectoryName(path);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
        }

        private void InitializeSchema()
        {
            using (var connection = new SqliteConnection(_connectionString))
            {
                connection.Open();
                var command = new SqliteCommand(@"
                    CREATE TABLE IF NOT EXISTS Categories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Color TEXT,
                        CreatedDate DATETIME
                    );
                    CREATE TABLE IF NOT EXISTS Tasks (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            Title TEXT NOT NULL,
                            Description TEXT,
                            DueDate DATETIME,
                            Priority INTEGER DEFAULT 0,
                            Status INTEGER DEFAULT 0,
                            CategoryId INTEGER,
                            IsRecurring BOOLEAN DEFAULT FALSE,
                            RecurrencePattern TEXT,
                            CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                            CompletedDate DATETIME,
                            FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
                        );
                        CREATE TABLE IF NOT EXISTS Reminders (
                            Id INTEGER PRIMARY KEY AUTOINCREMENT,
                            TaskId INTEGER NOT NULL,
                            ReminderTime DATETIME NOT NULL,
                            IsActive BOOLEAN DEFAULT TRUE,
                            FOREIGN KEY (TaskId) REFERENCES Tasks(Id)
                        );
                        INSERT OR IGNORE INTO Categories (Id, Name, Color, CreatedDate) VALUES (1, 'General', '#808080', CURRENT_TIMESTAMP);", connection);
                command.ExecuteNonQuery();
            }
        }

        private SqliteConnection GetConnection()
        {
            if (_connection == null)
            {
                _connection = new SqliteConnection(_connectionString);
                _connection.Open();
            }
            return _connection;
        }

        // CRUD for Tasks
        public List<TaskModel> GetTasks()
        {
            var tasks = new List<TaskModel>();
            try
            {
                var command = new SqliteCommand(@"SELECT * FROM Tasks", GetConnection());
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        tasks.Add(new TaskModel
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("Id")),
                            Title = reader.GetString(reader.GetOrdinal("Title")),
                            Description = reader.IsDBNull(reader.GetOrdinal("Description")) ? null : reader.GetString(reader.GetOrdinal("Description")),
                            DueDate = reader.IsDBNull(reader.GetOrdinal("DueDate")) ? null : reader.GetDateTime(reader.GetOrdinal("DueDate")),
                            Priority = reader.GetInt32(reader.GetOrdinal("Priority")),
                            Status = reader.GetInt32(reader.GetOrdinal("Status")),
                            CategoryId = reader.IsDBNull(reader.GetOrdinal("CategoryId")) ? null : reader.GetInt32(reader.GetOrdinal("CategoryId")),
                            IsRecurring = reader.GetBoolean(reader.GetOrdinal("IsRecurring")),
                            RecurrencePattern = reader.IsDBNull(reader.GetOrdinal("RecurrencePattern")) ? null : reader.GetString(reader.GetOrdinal("RecurrencePattern")),
                            CreatedDate = reader.GetDateTime(reader.GetOrdinal("CreatedDate")),
                            CompletedDate = reader.IsDBNull(reader.GetOrdinal("CompletedDate")) ? null : reader.GetDateTime(reader.GetOrdinal("CompletedDate"))
                        });
                    }
                }
                Log.Information("Retrieved {Count} tasks", tasks.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error retrieving tasks");
                throw;
            }
            return tasks;
        }

        public void UpdateTask(TaskModel task)
        {
            try
            {
                var command = new SqliteCommand(@"UPDATE Tasks SET Title = @title, Description = @description,
                    DueDate = @dueDate, Priority = @priority, Status = @status, CategoryId = @categoryId,
                    IsRecurring = @isRecurring, RecurrencePattern = @recurrencePattern, CompletedDate = @completedDate
                    WHERE Id = @id", GetConnection());

                command.Parameters.AddWithValue("@title", task.Title);
                command.Parameters.AddWithValue("@description", task.Description ?? "");
                command.Parameters.AddWithValue("@dueDate", task.DueDate == null ? DBNull.Value : task.DueDate);
                command.Parameters.AddWithValue("@priority", task.Priority);
                command.Parameters.AddWithValue("@status", task.Status);
                command.Parameters.AddWithValue("@categoryId", task.CategoryId == null ? DBNull.Value : task.CategoryId);
                command.Parameters.AddWithValue("@isRecurring", task.IsRecurring);
                command.Parameters.AddWithValue("@recurrencePattern", task.RecurrencePattern ?? "");
                command.Parameters.AddWithValue("@completedDate", task.CompletedDate == null ? DBNull.Value : task.CompletedDate);
                command.Parameters.AddWithValue("@id", task.Id);

                command.ExecuteNonQuery();
                Log.Information("Updated task {Id}", task.Id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error updating task {Id}", task.Id);
                throw;
            }
        }

        public void InsertTask(TaskModel task)
        {
            try
            {
                var command = new SqliteCommand(@"INSERT INTO Tasks (Title, Description, DueDate, Priority, Status, IsRecurring, RecurrencePattern, CreatedDate)
                    VALUES (@title, @description, @dueDate, @priority, @status, @isRecurring, @recurrencePattern, @createdDate);
                    SELECT last_insert_rowid();", GetConnection());

                command.Parameters.AddWithValue("@title", task.Title ?? "");
                command.Parameters.AddWithValue("@description", task.Description ?? "");
                command.Parameters.AddWithValue("@dueDate", task.DueDate == null ? DBNull.Value : task.DueDate);
                command.Parameters.AddWithValue("@priority", task.Priority);
                command.Parameters.AddWithValue("@status", task.Status);
                command.Parameters.AddWithValue("@isRecurring", task.IsRecurring);
                command.Parameters.AddWithValue("@recurrencePattern", task.RecurrencePattern ?? "");
                command.Parameters.AddWithValue("@createdDate", task.CreatedDate);

                task.Id = (int)(long)command.ExecuteScalar();
                Log.Information("Inserted task {Id}", task.Id);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Error inserting task: {Message}", ex.Message);
                throw;
            }
        }

        public void Dispose()
        {
            if (_connection != null)
            {
                _connection.Close();
                _connection.Dispose();
            }
        }
    }
}