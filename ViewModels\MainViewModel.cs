using TaskModel = TaskReminderApp.Models.Task;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Collections.ObjectModel;
using System.Linq;
using TaskReminderApp.Services;
using TaskReminderApp.Models;
using System.Windows.Input;

namespace TaskReminderApp.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged
    {
        private readonly DataService _dataService;
        private ObservableCollection<TaskReminderApp.Models.Task> _tasks;
        private string _appTitle;
        private string _currentDateTime;
        private string _statusBarText;

        public event PropertyChangedEventHandler PropertyChanged;

        private void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public MainViewModel()
        {
            _dataService = new DataService();
            AppTitle = "Task Reminder Application";
            CurrentDateTime = DateTime.Now.ToString("dddd, MMMM dd, yyyy - HH:mm");
            StatusBarText = "Ready";

            Help_Command = new RelayCommand(Help_Executed);
            Exit_Command = new RelayCommand(Exit_Executed);

            LoadTasks();
            UpdateTaskStats();
        }

        public ObservableCollection<TaskModel> Tasks
        {
            get => _tasks;
            set { _tasks = value; OnPropertyChanged(); }
        }

        public string AppTitle
        {
            get => _appTitle;
            set { _appTitle = value; OnPropertyChanged(); }
        }

        public string CurrentDateTime
        {
            get => _currentDateTime;
            set { _currentDateTime = value; OnPropertyChanged(); }
        }

        public string StatusBarText
        {
            get => _statusBarText;
            set { _statusBarText = value; OnPropertyChanged(); }
        }

        private void LoadTasks()
        {
            try
            {
                var taskList = _dataService.GetTasks();
                Tasks = new ObservableCollection<TaskReminderApp.Models.Task>(taskList);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading tasks: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateTaskStats()
        {
            if (Tasks != null)
            {
                int totalTasks = Tasks.Count;
                int pendingTasks = Tasks.Count(t => t.Status == 0);
                int completedTasks = Tasks.Count(t => t.Status == 2);
                int overdueTasks = Tasks.Count(t => t.Status == 3);

                // Update StatusBarText with stats
                StatusBarText = $"Total: {totalTasks}, Pending: {pendingTasks}, Completed: {completedTasks}, Overdue: {overdueTasks}";
            }
        }

        // Commands
        public ICommand Help_Command { get; private set; }
        public ICommand Exit_Command { get; private set; }

        private void Help_Executed(object? parameter)
        {
            MessageBox.Show("Task Reminder Application\n\nHelp: This is a task management tool with reminders.", "Help", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void Exit_Executed(object? parameter)
        {
            App.Current.Shutdown();
        }

        public class RelayCommand : ICommand
        {
            private readonly Action<object?> _execute;
            private readonly Predicate<object?> _canExecute;

            public RelayCommand(Action<object?> execute) : this(execute, null) { }

            public RelayCommand(Action<object?> execute, Predicate<object?> canExecute)
            {
                _execute = execute ?? throw new ArgumentNullException("execute");
                _canExecute = canExecute;
            }

            public bool CanExecute(object? parameter)
            {
                return _canExecute == null || _canExecute(parameter);
            }

            public void Execute(object? parameter)
            {
                _execute(parameter);
            }

            public event EventHandler? CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }
        }
    }
}