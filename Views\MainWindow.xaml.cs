﻿using System;
using System.Windows;

namespace TaskReminderApp.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        try
        {
            InitializeComponent();
            // Temporarily comment out the ViewModel to test basic WPF functionality
            // DataContext = new TaskReminderApp.ViewModels.MainViewModel();
            MessageBox.Show("MainWindow initialized successfully!", "Success", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing MainWindow: {ex.Message}\n\nStack trace:\n{ex.StackTrace}",
                          "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
            throw;
        }
    }
}