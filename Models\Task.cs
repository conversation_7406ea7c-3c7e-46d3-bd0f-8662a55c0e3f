using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TaskReminderApp.Models
{
    public class Task
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; }

        public string Description { get; set; }

        public DateTime? DueDate { get; set; }

        public int Priority { get; set; } = 0; // 0=None, 1=Low, 2=Medium, 3=High

        public int Status { get; set; } = 0; // 0=Pending, 1=In Progress, 2=Completed, 3=Overdue

        public int? CategoryId { get; set; }

        public bool IsRecurring { get; set; } = false;

        public string RecurrencePattern { get; set; } // JSON or text for recurrence

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? CompletedDate { get; set; }

        // Navigation properties
        [ForeignKey("CategoryId")]
        public virtual Category Category { get; set; }

        public virtual ICollection<Reminder> Reminders { get; set; }
    }
}