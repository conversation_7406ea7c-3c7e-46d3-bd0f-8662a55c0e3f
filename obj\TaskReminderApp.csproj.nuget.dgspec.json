{"format": 1, "restore": {"G:\\vscodeapps\\TaskReminderApp\\TaskReminderApp.csproj": {}}, "projects": {"G:\\vscodeapps\\TaskReminderApp\\TaskReminderApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "G:\\vscodeapps\\TaskReminderApp\\TaskReminderApp.csproj", "projectName": "TaskReminderApp", "projectPath": "G:\\vscodeapps\\TaskReminderApp\\TaskReminderApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "G:\\vscodeapps\\TaskReminderApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"AutoMapper": {"target": "Package", "version": "[15.0.1, )"}, "Microsoft.Data.Sqlite": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Serilog": {"target": "Package", "version": "[4.3.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.410/PortableRuntimeIdentifierGraph.json"}}}}}