-- SQLite Database Schema for Task Reminder Application

CREATE TABLE Categories (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Name TEXT NOT NULL UNIQUE,
    Color TEXT,
    CreatedDate DATETIME
);

CREATE TABLE Tasks (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Title TEXT NOT NULL,
    Description TEXT,
    DueDate DATETIME,
    Priority INTEGER DEFAULT 0,
    Status INTEGER DEFAULT 0,
    CategoryId INTEGER,
    IsRecurring BOOLEAN DEFAULT FALSE,
    RecurrencePattern TEXT,
    CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
    CompletedDate DATETIME,
    FOREIGN KEY (CategoryId) REFERENCES Categories(Id)
);

CREATE TABLE Reminders (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    TaskId INTEGER NOT NULL,
    ReminderTime DATETIME NOT NULL,
    IsActive BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (TaskId) REFERENCES Tasks(Id)
);

-- Indexes for performance
CREATE INDEX IX_Tasks_DueDate ON Tasks(DueDate);
CREATE INDEX IX_Tasks_Priority ON Tasks(Priority);
CREATE INDEX IX_Tasks_Status ON Tasks(Status);
CREATE INDEX IX_Reminders_ReminderTime ON Reminders(ReminderTime);

-- Insert default category
INSERT INTO Categories (Name, Color, CreatedDate) VALUES ('General', '#808080', CURRENT_TIMESTAMP);