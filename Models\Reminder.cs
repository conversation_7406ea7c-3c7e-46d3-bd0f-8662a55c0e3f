using System.ComponentModel.DataAnnotations;

namespace TaskReminderApp.Models
{
    public class Reminder
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int TaskId { get; set; }

        [Required]
        public DateTime ReminderTime { get; set; }

        public bool IsActive { get; set; } = true;

        // Navigation property
        public virtual Task Task { get; set; }
    }
}